import React, { useState, useRef, useCallback } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  IconButton,
  Avatar,
  AvatarGroup,
  Chip,
  Badge,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Divider,
  Stack,
  TextField,
  Tooltip,
  Card,
  CardContent,
  Grid,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Menu,
  MenuItem,
  Select,
  FormControl,
  InputLabel,
  Switch,
  FormControlLabel,
  Breadcrumbs,
  Link,
  Drawer,
  Fab,
  SpeedDial,
  SpeedDialAction,
  SpeedDialIcon,
  Autocomplete,
  Popper,
  ClickAwayListener,
  Grow
} from '@mui/material';
import {
  Edit as EditIcon,
  Settings as SettingsIcon,
  Group as GroupIcon,
  Chat as ChatIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  Send as SendIcon,
  ExpandMore as ExpandMoreIcon,
  Assignment as TaskIcon,
  BugReport as TestIcon,
  History as HistoryIcon,
  LocalOffer as TagIcon,
  CheckCircle as ApproveIcon,
  PlayArrow as PlayIcon,
  Pause as PauseIcon,
  Stop as StopIcon,
  Link as LinkIcon,
  Comment as CommentIcon,
  ExpandLess as CollapseIcon,
  ExpandMore as ExpandIcon,
  Search as SearchIcon,
  DragIndicator as DragIcon,
  Person as PersonIcon,
  Schedule as ScheduleIcon,
  Done as DoneIcon,
  Pending as PendingIcon,
  Home as HomeIcon,
  Folder as FolderIcon,
  Extension as FeatureIcon,
  Menu as MenuIcon,
  Close as CloseIcon,
  Navigation as NavigationIcon,
  ViewList as ViewListIcon,
  Dashboard as DashboardIcon,
  ArrowBack as ArrowBackIcon,
  ArrowForward as ArrowForwardIcon
} from '@mui/icons-material';

// Enhanced mock data with more detailed tasks and states
const mockFeature = {
  id: 'F-001',
  title: 'User Authentication System',
  description: `
    <h3>Overview</h3>
    <p>Implement a comprehensive user authentication system with multi-factor authentication support.</p>
    
    <h4>Key Requirements:</h4>
    <ul>
      <li>Email/password login</li>
      <li>OAuth integration (Google, GitHub)</li>
      <li>Two-factor authentication</li>
      <li>Password reset functionality</li>
    </ul>
    
    <h4>Design Mockups:</h4>
    <p><em>[Mockup images would be embedded here]</em></p>
    
    <h4>Technical Considerations:</h4>
    <p>The system should use JWT tokens for session management and integrate with our existing user management infrastructure.</p>
    
    <blockquote>
      <strong>Note:</strong> This description was updated on 2024-01-15 to include OAuth requirements. 
      <a href="#comment-5">See discussion in comments →</a>
    </blockquote>
  `,
  state: 'In Progress',
  version: 'v2.1',
  activeUsers: [
    { id: 1, name: 'Alice Johnson', avatar: 'AJ', color: '#1976d2', status: 'editing description' },
    { id: 2, name: 'Bob Smith', avatar: 'BS', color: '#388e3c', status: 'reviewing tests' },
    { id: 3, name: 'Carol Davis', avatar: 'CD', color: '#f57c00', status: 'viewing' }
  ],
  team: [
    { id: 1, name: 'Alice Johnson', role: 'Lead Developer', avatar: 'AJ', color: '#1976d2', canApprove: true },
    { id: 2, name: 'Bob Smith', role: 'QA Engineer', avatar: 'BS', color: '#388e3c', canApprove: false },
    { id: 3, name: 'Carol Davis', role: 'Product Manager', avatar: 'CD', color: '#f57c00', canApprove: true },
    { id: 4, name: 'David Wilson', role: 'UI Designer', avatar: 'DW', color: '#7b1fa2', canApprove: false }
  ],
  tasks: [
    { id: 1, title: 'Set up OAuth providers configuration', assignee: 'Alice Johnson', assigneeId: 1, status: 'active', priority: 'high', estimatedHours: 8, actualHours: 3 },
    { id: 2, title: 'Design login UI mockups and wireframes', assignee: 'David Wilson', assigneeId: 4, status: 'completed', priority: 'medium', estimatedHours: 12, actualHours: 14 },
    { id: 3, title: 'Write comprehensive authentication tests', assignee: 'Bob Smith', assigneeId: 2, status: 'active', priority: 'high', estimatedHours: 16, actualHours: 8 },
    { id: 4, title: 'Implement password reset flow', assignee: 'Alice Johnson', assigneeId: 1, status: 'pending', priority: 'medium', estimatedHours: 6, actualHours: 0 },
    { id: 5, title: 'Review security requirements', assignee: 'Carol Davis', assigneeId: 3, status: 'active', priority: 'high', estimatedHours: 4, actualHours: 2 }
  ],
  approvals: [
    { id: 1, version: 'v2.1', approver: 'Carol Davis', approverId: 3, status: 'pending', type: 'requirements' },
    { id: 2, version: 'v2.1', approver: 'Alice Johnson', approverId: 1, status: 'approved', type: 'technical' },
    { id: 3, version: 'v2.0', approver: 'Carol Davis', approverId: 3, status: 'approved', type: 'requirements' }
  ],
  comments: [
    { 
      id: 1, 
      author: 'Alice Johnson', 
      avatar: 'AJ', 
      color: '#1976d2', 
      time: '2 hours ago', 
      text: 'I think we should prioritize the OAuth integration first since it affects the overall architecture. This will require significant changes to our current authentication flow and we need to ensure backward compatibility.', 
      linkedToDescription: false,
      expanded: false
    },
    { 
      id: 2, 
      author: 'Bob Smith', 
      avatar: 'BS', 
      color: '#388e3c', 
      time: '1 hour ago', 
      text: 'Agreed. I\'ve started writing test cases for the OAuth flow. Should we support SAML as well? I\'ve seen some enterprise customers asking for it.', 
      linkedToDescription: false,
      expanded: false
    },
    { 
      id: 3, 
      author: 'Carol Davis', 
      avatar: 'CD', 
      color: '#f57c00', 
      time: '45 minutes ago', 
      text: 'Let\'s stick with OAuth for now. We can add SAML in a future iteration if customers request it. I want to keep the scope manageable for this release.', 
      linkedToDescription: false,
      expanded: false
    },
    { 
      id: 4, 
      author: 'Alice Johnson', 
      avatar: 'AJ', 
      color: '#1976d2', 
      time: '30 minutes ago', 
      text: 'Perfect. I\'ll update the technical requirements accordingly and create the necessary tasks for the team.', 
      linkedToDescription: false,
      expanded: false
    },
    { 
      id: 5, 
      author: 'Carol Davis', 
      avatar: 'CD', 
      color: '#f57c00', 
      time: '15 minutes ago', 
      text: 'I\'ve added OAuth requirements to the description. This affects our timeline - we might need an extra sprint. The complexity is higher than initially estimated and we need to coordinate with the infrastructure team for deployment.', 
      linkedToDescription: true,
      expanded: false
    }
  ],
  testResults: { pass: 12, fail: 3, skip: 2 },
  releaseTags: ['v2.1-auth', 'security-update'],
  documentHistory: 3
};

// Mock navigation data
const mockNavigationData = {
  currentProject: {
    id: 'proj-001',
    name: 'Authentication System Project',
    features: [
      { id: 'F-001', name: 'User Authentication System', hasActiveTasks: true },
      { id: 'F-002', name: 'Password Reset Flow', hasActiveTasks: false },
      { id: 'F-003', name: 'OAuth Integration', hasActiveTasks: true }
    ],
    requirements: [
      { id: 'R-001', name: 'Login API Endpoint', hasActiveTasks: true },
      { id: 'R-002', name: 'User Session Management', hasActiveTasks: false }
    ]
  },
  recentProjects: [
    { id: 'proj-002', name: 'E-commerce Platform' },
    { id: 'proj-003', name: 'Mobile App Backend' },
    { id: 'proj-004', name: 'Analytics Dashboard' }
  ],
  allProjects: [
    { id: 'proj-001', name: 'Authentication System Project' },
    { id: 'proj-002', name: 'E-commerce Platform' },
    { id: 'proj-003', name: 'Mobile App Backend' },
    { id: 'proj-004', name: 'Analytics Dashboard' },
    { id: 'proj-005', name: 'Customer Support Portal' },
    { id: 'proj-006', name: 'Inventory Management' }
  ]
};

const CollaborativeLayoutTest3 = () => {
  const [newComment, setNewComment] = useState('');
  const [editMode, setEditMode] = useState(false);
  const [commentsWidth, setCommentsWidth] = useState(33); // percentage
  const [isResizing, setIsResizing] = useState(false);
  const [comments, setComments] = useState(mockFeature.comments);
  const [allCommentsExpanded, setAllCommentsExpanded] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [tasks, setTasks] = useState(mockFeature.tasks);
  const [approvals, setApprovals] = useState(mockFeature.approvals);
  const [selectedTask, setSelectedTask] = useState(null);
  const [taskMenuAnchor, setTaskMenuAnchor] = useState(null);

  // Navigation state
  const [navigationMode, setNavigationMode] = useState('breadcrumb'); // 'breadcrumb', 'compact', 'floating', 'hidden'
  const [miniNavOpen, setMiniNavOpen] = useState(false);
  const [projectSearchOpen, setProjectSearchOpen] = useState(false);
  const [projectSearchAnchor, setProjectSearchAnchor] = useState(null);
  
  const resizeRef = useRef(null);
  const containerRef = useRef(null);

  const handleMouseDown = useCallback((e) => {
    setIsResizing(true);
    e.preventDefault();
  }, []);

  const handleMouseMove = useCallback((e) => {
    if (!isResizing || !containerRef.current) return;
    
    const containerRect = containerRef.current.getBoundingClientRect();
    const newWidth = ((containerRect.right - e.clientX) / containerRect.width) * 100;
    
    // Limit between 25% and 60%
    const clampedWidth = Math.max(25, Math.min(60, newWidth));
    setCommentsWidth(clampedWidth);
  }, [isResizing]);

  const handleMouseUp = useCallback(() => {
    setIsResizing(false);
  }, []);

  React.useEffect(() => {
    if (isResizing) {
      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
      return () => {
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
      };
    }
  }, [isResizing, handleMouseMove, handleMouseUp]);

  const handleSendComment = () => {
    if (newComment.trim()) {
      const newCommentObj = {
        id: comments.length + 1,
        author: 'Current User',
        avatar: 'CU',
        color: '#9c27b0',
        time: 'just now',
        text: newComment,
        linkedToDescription: false,
        expanded: false
      };
      setComments([...comments, newCommentObj]);
      setNewComment('');
    }
  };

  const toggleCommentExpansion = (commentId) => {
    setComments(comments.map(comment => 
      comment.id === commentId 
        ? { ...comment, expanded: !comment.expanded }
        : comment
    ));
  };

  const toggleAllComments = () => {
    const newExpandedState = !allCommentsExpanded;
    setAllCommentsExpanded(newExpandedState);
    setComments(comments.map(comment => ({ ...comment, expanded: newExpandedState })));
  };

  const filteredComments = comments.filter(comment =>
    comment.text.toLowerCase().includes(searchTerm.toLowerCase()) ||
    comment.author.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const truncateText = (text, maxLength = 100) => {
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };

  const handleTaskAction = (taskId, action) => {
    setTasks(tasks.map(task => {
      if (task.id === taskId) {
        switch (action) {
          case 'start':
            return { ...task, status: 'active' };
          case 'pause':
            return { ...task, status: 'pending' };
          case 'complete':
            return { ...task, status: 'completed' };
          default:
            return task;
        }
      }
      return task;
    }));
    setTaskMenuAnchor(null);
    setSelectedTask(null);
  };

  const handleApprovalAction = (approvalId, action) => {
    setApprovals(approvals.map(approval => {
      if (approval.id === approvalId) {
        return { ...approval, status: action };
      }
      return approval;
    }));
  };

  const getTaskStatusColor = (status) => {
    switch (status) {
      case 'active': return 'primary';
      case 'completed': return 'success';
      case 'pending': return 'warning';
      default: return 'default';
    }
  };

  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'high': return 'error';
      case 'medium': return 'warning';
      case 'low': return 'info';
      default: return 'default';
    }
  };

  // Navigation mode selector
  const NavigationModeSelector = () => (
    <Paper sx={{ p: 2, mb: 3, borderRadius: 3 }}>
      <Typography variant="h6" gutterBottom>Navigation Test Mode</Typography>
      <Stack direction="row" spacing={2} flexWrap="wrap">
        <Button
          variant={navigationMode === 'breadcrumb' ? 'contained' : 'outlined'}
          onClick={() => setNavigationMode('breadcrumb')}
          size="small"
        >
          Smart Breadcrumb
        </Button>
        <Button
          variant={navigationMode === 'compact' ? 'contained' : 'outlined'}
          onClick={() => setNavigationMode('compact')}
          size="small"
        >
          Compact Header
        </Button>
        <Button
          variant={navigationMode === 'floating' ? 'contained' : 'outlined'}
          onClick={() => setNavigationMode('floating')}
          size="small"
        >
          Floating Menu
        </Button>
        <Button
          variant={navigationMode === 'hidden' ? 'contained' : 'outlined'}
          onClick={() => setNavigationMode('hidden')}
          size="small"
        >
          Hidden (Max Space)
        </Button>
      </Stack>
    </Paper>
  );

  return (
    <Box sx={{ p: 3, bgcolor: 'background.default', minHeight: '100vh' }}>
      <NavigationModeSelector />

      {/* Navigation Components */}
      {navigationMode === 'breadcrumb' && <SmartBreadcrumbNavigation />}
      {navigationMode === 'compact' && <CompactHeaderNavigation />}
      {navigationMode === 'floating' && <FloatingMenuNavigation />}

      {/* Header */}
      <Paper sx={{ p: 3, mb: 3, borderRadius: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
          <Box sx={{ flex: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
              <Typography variant="h4" component="h1">
                {mockFeature.title}
              </Typography>
              <Chip label={mockFeature.state} color="primary" size="small" />
              <Chip label={mockFeature.version} variant="outlined" size="small" />
            </Box>
            
            {/* Live Collaboration Indicators */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Active now:
              </Typography>
              <AvatarGroup max={4} sx={{ '& .MuiAvatar-root': { width: 32, height: 32, fontSize: '0.875rem' } }}>
                {mockFeature.activeUsers.map(user => (
                  <Tooltip key={user.id} title={`${user.name} (${user.status})`}>
                    <Avatar sx={{ bgcolor: user.color, width: 32, height: 32 }}>
                      {user.avatar}
                    </Avatar>
                  </Tooltip>
                ))}
              </AvatarGroup>
              <Chip 
                icon={<VisibilityIcon />} 
                label={`${mockFeature.activeUsers.length} viewing`} 
                size="small" 
                variant="outlined" 
              />
            </Box>
          </Box>
          
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant="outlined"
              startIcon={<EditIcon />}
              onClick={() => setEditMode(!editMode)}
            >
              {editMode ? 'Save' : 'Edit'}
            </Button>
            <IconButton>
              <SettingsIcon />
            </IconButton>
          </Box>
        </Box>
      </Paper>

      {/* Main Content Area with Resizable Comments */}
      <Box ref={containerRef} sx={{ display: 'flex', gap: 3, position: 'relative' }}>
        {/* Left: Description */}
        <Paper sx={{ 
          flex: 1, 
          width: `${100 - commentsWidth}%`,
          p: 3, 
          borderRadius: 3,
          transition: isResizing ? 'none' : 'width 0.2s ease'
        }}>
          <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <EditIcon color="primary" />
            Description
            {editMode && <Chip label="Editing" color="warning" size="small" />}
          </Typography>
          <Box 
            sx={{ 
              minHeight: 400,
              border: editMode ? '2px dashed #1976d2' : 'none',
              borderRadius: 1,
              p: editMode ? 2 : 0
            }}
            dangerouslySetInnerHTML={{ __html: mockFeature.description }}
          />
        </Paper>

        {/* Resize Handle */}
        <Box
          ref={resizeRef}
          onMouseDown={handleMouseDown}
          sx={{
            width: 8,
            cursor: 'col-resize',
            bgcolor: isResizing ? 'primary.main' : 'divider',
            borderRadius: 1,
            '&:hover': {
              bgcolor: 'primary.main',
              opacity: 0.7
            },
            transition: 'background-color 0.2s ease',
            position: 'relative',
            '&::before': {
              content: '""',
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              width: 20,
              height: 40,
              cursor: 'col-resize'
            }
          }}
        >
          <DragIcon sx={{
            position: 'absolute',
            top: '50%',
            left: '50%',
            transform: 'translate(-50%, -50%)',
            fontSize: 16,
            color: 'text.secondary'
          }} />
        </Box>

        {/* Right: Enhanced Comments */}
        <Paper sx={{
          width: `${commentsWidth}%`,
          borderRadius: 3,
          position: 'sticky',
          top: 24,
          height: 'fit-content',
          maxHeight: 'calc(100vh - 200px)',
          transition: isResizing ? 'none' : 'width 0.2s ease'
        }}>
          {/* Comments Header with Controls */}
          <Box sx={{ p: 2, borderBottom: 1, borderColor: 'divider' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
              <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <ChatIcon color="primary" />
                Comments
                <Badge badgeContent={filteredComments.length} color="primary" />
              </Typography>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Tooltip title={allCommentsExpanded ? "Collapse all" : "Expand all"}>
                  <IconButton size="small" onClick={toggleAllComments}>
                    {allCommentsExpanded ? <CollapseIcon /> : <ExpandIcon />}
                  </IconButton>
                </Tooltip>
              </Box>
            </Box>

            {/* Search Comments */}
            <TextField
              fullWidth
              size="small"
              placeholder="Search comments..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              InputProps={{
                startAdornment: <SearchIcon sx={{ mr: 1, color: 'text.secondary' }} />
              }}
            />
          </Box>

          {/* Comments List */}
          <Box sx={{ maxHeight: 400, overflow: 'auto', p: 2 }}>
            {filteredComments.map((comment) => (
              <Box key={comment.id} sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 1 }}>
                  <Avatar sx={{ bgcolor: comment.color, width: 32, height: 32, fontSize: '0.75rem' }}>
                    {comment.avatar}
                  </Avatar>
                  <Box sx={{ flex: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 0.5 }}>
                      <Typography variant="subtitle2">{comment.author}</Typography>
                      <Typography variant="caption" color="text.secondary">{comment.time}</Typography>
                      {comment.linkedToDescription && (
                        <Tooltip title="Linked to description change">
                          <LinkIcon sx={{ fontSize: 16, color: 'primary.main' }} />
                        </Tooltip>
                      )}
                    </Box>
                    <Typography
                      variant="body2"
                      sx={{
                        cursor: comment.text.length > 100 ? 'pointer' : 'default',
                        '&:hover': comment.text.length > 100 ? { bgcolor: 'action.hover', borderRadius: 1, p: 0.5, m: -0.5 } : {}
                      }}
                      onClick={() => comment.text.length > 100 && toggleCommentExpansion(comment.id)}
                    >
                      {comment.expanded || comment.text.length <= 100
                        ? comment.text
                        : truncateText(comment.text)
                      }
                      {comment.text.length > 100 && (
                        <Button
                          size="small"
                          sx={{ ml: 1, minWidth: 'auto', p: 0 }}
                          onClick={(e) => {
                            e.stopPropagation();
                            toggleCommentExpansion(comment.id);
                          }}
                        >
                          {comment.expanded ? 'Show less' : 'Show more'}
                        </Button>
                      )}
                    </Typography>
                  </Box>
                </Box>
              </Box>
            ))}

            {filteredComments.length === 0 && searchTerm && (
              <Typography variant="body2" color="text.secondary" textAlign="center" sx={{ py: 2 }}>
                No comments found matching "{searchTerm}"
              </Typography>
            )}
          </Box>

          {/* Add Comment */}
          <Box sx={{ p: 2, borderTop: 1, borderColor: 'divider' }}>
            <TextField
              fullWidth
              multiline
              rows={2}
              placeholder="Add a comment..."
              value={newComment}
              onChange={(e) => setNewComment(e.target.value)}
              sx={{ mb: 1 }}
            />
            <Button
              variant="contained"
              endIcon={<SendIcon />}
              onClick={handleSendComment}
              disabled={!newComment.trim()}
              size="small"
            >
              Send
            </Button>
          </Box>
        </Paper>
      </Box>

      {/* Enhanced Team & Tasks Management */}
      <Box sx={{ mt: 3 }}>
        <Grid container spacing={3}>
          {/* Tasks Overview */}
          <Grid item xs={12} md={8}>
            <Paper sx={{ p: 3, borderRadius: 3 }}>
              <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <TaskIcon color="primary" />
                Tasks & Workflow
                <Badge badgeContent={tasks.filter(t => t.status === 'active').length} color="warning" />
              </Typography>

              <TableContainer>
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Task</TableCell>
                      <TableCell>Assignee</TableCell>
                      <TableCell>Status</TableCell>
                      <TableCell>Priority</TableCell>
                      <TableCell>Progress</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {tasks.map((task) => {
                      const assignee = mockFeature.team.find(member => member.id === task.assigneeId);
                      const progress = task.actualHours / task.estimatedHours * 100;

                      return (
                        <TableRow key={task.id} hover>
                          <TableCell>
                            <Typography variant="body2" fontWeight="medium">
                              {task.title}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {task.estimatedHours}h estimated • {task.actualHours}h logged
                            </Typography>
                          </TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Avatar sx={{ bgcolor: assignee?.color, width: 24, height: 24, fontSize: '0.7rem' }}>
                                {assignee?.avatar}
                              </Avatar>
                              <Typography variant="body2">{task.assignee}</Typography>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={task.status}
                              size="small"
                              color={getTaskStatusColor(task.status)}
                              variant={task.status === 'active' ? 'filled' : 'outlined'}
                            />
                          </TableCell>
                          <TableCell>
                            <Chip
                              label={task.priority}
                              size="small"
                              color={getPriorityColor(task.priority)}
                              variant="outlined"
                            />
                          </TableCell>
                          <TableCell>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                              <Box sx={{
                                width: 60,
                                height: 6,
                                bgcolor: 'grey.300',
                                borderRadius: 3,
                                overflow: 'hidden'
                              }}>
                                <Box sx={{
                                  width: `${Math.min(progress, 100)}%`,
                                  height: '100%',
                                  bgcolor: progress > 100 ? 'error.main' : 'primary.main',
                                  transition: 'width 0.3s ease'
                                }} />
                              </Box>
                              <Typography variant="caption" color={progress > 100 ? 'error.main' : 'text.secondary'}>
                                {Math.round(progress)}%
                              </Typography>
                            </Box>
                          </TableCell>
                          <TableCell>
                            <Stack direction="row" spacing={0.5}>
                              {task.status === 'pending' && (
                                <IconButton
                                  size="small"
                                  color="primary"
                                  onClick={() => handleTaskAction(task.id, 'start')}
                                  title="Start Task"
                                >
                                  <PlayIcon fontSize="small" />
                                </IconButton>
                              )}
                              {task.status === 'active' && (
                                <>
                                  <IconButton
                                    size="small"
                                    color="warning"
                                    onClick={() => handleTaskAction(task.id, 'pause')}
                                    title="Pause Task"
                                  >
                                    <PauseIcon fontSize="small" />
                                  </IconButton>
                                  <IconButton
                                    size="small"
                                    color="success"
                                    onClick={() => handleTaskAction(task.id, 'complete')}
                                    title="Complete Task"
                                  >
                                    <DoneIcon fontSize="small" />
                                  </IconButton>
                                </>
                              )}
                              {task.status === 'completed' && (
                                <Chip label="Done" size="small" color="success" icon={<DoneIcon />} />
                              )}
                            </Stack>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </TableContainer>

              <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
                <Button variant="outlined" startIcon={<TaskIcon />} size="small">
                  Add Task
                </Button>
                <Button variant="outlined" startIcon={<PersonIcon />} size="small">
                  Assign Tasks
                </Button>
              </Box>
            </Paper>
          </Grid>

          {/* Approvals & Team */}
          <Grid item xs={12} md={4}>
            <Stack spacing={3}>
              {/* Approvals */}
              <Paper sx={{ p: 3, borderRadius: 3 }}>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <ApproveIcon color="success" />
                  Approvals
                  <Badge badgeContent={approvals.filter(a => a.status === 'pending').length} color="warning" />
                </Typography>

                {approvals.map((approval) => {
                  const approver = mockFeature.team.find(member => member.id === approval.approverId);

                  return (
                    <Box key={approval.id} sx={{ mb: 2, p: 2, border: 1, borderColor: 'divider', borderRadius: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                        <Avatar sx={{ bgcolor: approver?.color, width: 24, height: 24, fontSize: '0.7rem' }}>
                          {approver?.avatar}
                        </Avatar>
                        <Box sx={{ flex: 1 }}>
                          <Typography variant="body2" fontWeight="medium">
                            {approval.type} approval
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            {approval.version} • {approval.approver}
                          </Typography>
                        </Box>
                        <Chip
                          label={approval.status}
                          size="small"
                          color={approval.status === 'approved' ? 'success' : 'warning'}
                          icon={approval.status === 'approved' ? <DoneIcon /> : <PendingIcon />}
                        />
                      </Box>

                      {approval.status === 'pending' && approver?.canApprove && (
                        <Stack direction="row" spacing={1}>
                          <Button
                            size="small"
                            variant="contained"
                            color="success"
                            onClick={() => handleApprovalAction(approval.id, 'approved')}
                          >
                            Approve
                          </Button>
                          <Button
                            size="small"
                            variant="outlined"
                            color="error"
                            onClick={() => handleApprovalAction(approval.id, 'rejected')}
                          >
                            Reject
                          </Button>
                        </Stack>
                      )}
                    </Box>
                  );
                })}
              </Paper>

              {/* Team Overview */}
              <Paper sx={{ p: 3, borderRadius: 3 }}>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <GroupIcon color="primary" />
                  Team
                  <Badge badgeContent={mockFeature.team.length} color="secondary" />
                </Typography>

                {mockFeature.team.map((member) => {
                  const memberTasks = tasks.filter(task => task.assigneeId === member.id);
                  const activeTasks = memberTasks.filter(task => task.status === 'active').length;

                  return (
                    <Box key={member.id} sx={{ mb: 2, p: 2, border: 1, borderColor: 'divider', borderRadius: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                        <Avatar sx={{ bgcolor: member.color, width: 32, height: 32 }}>
                          {member.avatar}
                        </Avatar>
                        <Box sx={{ flex: 1 }}>
                          <Typography variant="body2" fontWeight="medium">{member.name}</Typography>
                          <Typography variant="caption" color="text.secondary">{member.role}</Typography>
                        </Box>
                        <Stack direction="row" spacing={0.5}>
                          <Chip
                            label={`${activeTasks} active`}
                            size="small"
                            color={activeTasks > 2 ? "warning" : "default"}
                          />
                          {member.canApprove && (
                            <Chip label="Approver" size="small" color="success" variant="outlined" />
                          )}
                        </Stack>
                      </Box>
                    </Box>
                  );
                })}

                <Button variant="contained" startIcon={<GroupIcon />} size="small" fullWidth>
                  Add Team Member
                </Button>
              </Paper>
            </Stack>
          </Grid>
        </Grid>
      </Box>

      {/* Navigation Components */}
      <MiniNavigationDrawer />
      <ProjectSearchPopper />
    </Box>
  );

  // Navigation Components
  function SmartBreadcrumbNavigation() {
    return (
      <Paper sx={{ p: 2, mb: 3, borderRadius: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Breadcrumbs aria-label="breadcrumb" sx={{ flex: 1 }}>
            <Link
              underline="hover"
              color="inherit"
              href="#"
              sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
            >
              <HomeIcon fontSize="small" />
              Dashboard
            </Link>
            <Link
              underline="hover"
              color="inherit"
              href="#"
              sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
            >
              <FolderIcon fontSize="small" />
              {mockNavigationData.currentProject.name}
            </Link>
            <Typography
              color="text.primary"
              sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}
            >
              <FeatureIcon fontSize="small" />
              {mockFeature.title}
            </Typography>
          </Breadcrumbs>

          <Box sx={{ display: 'flex', gap: 1 }}>
            {/* Quick Project Switcher */}
            <Button
              variant="outlined"
              size="small"
              startIcon={<NavigationIcon />}
              onClick={(e) => {
                setProjectSearchAnchor(e.currentTarget);
                setProjectSearchOpen(true);
              }}
            >
              Switch Project
            </Button>

            {/* Quick Access to Project Items */}
            <Button
              variant="outlined"
              size="small"
              startIcon={<ViewListIcon />}
              onClick={() => setMiniNavOpen(true)}
            >
              Project Items
            </Button>
          </Box>
        </Box>

        {/* Quick stats chips */}
        <Box sx={{ display: 'flex', gap: 1, mt: 2 }}>
          <Chip
            label={`${mockNavigationData.currentProject.features.filter(f => f.hasActiveTasks).length} active features`}
            size="small"
            color="primary"
            variant="outlined"
          />
          <Chip
            label={`${mockNavigationData.currentProject.requirements.filter(r => r.hasActiveTasks).length} active requirements`}
            size="small"
            color="warning"
            variant="outlined"
          />
        </Box>
      </Paper>
    );
  }

  function CompactHeaderNavigation() {
    return (
      <Paper sx={{ p: 1.5, mb: 3, borderRadius: 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          {/* Project Context */}
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <Avatar sx={{ bgcolor: 'primary.main', width: 32, height: 32 }}>
              <FolderIcon fontSize="small" />
            </Avatar>
            <Box>
              <Typography variant="body2" fontWeight="medium">
                {mockNavigationData.currentProject.name}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                {mockFeature.title}
              </Typography>
            </Box>
          </Box>

          <Divider orientation="vertical" flexItem />

          {/* Quick Actions */}
          <Box sx={{ display: 'flex', gap: 1, flex: 1 }}>
            <Autocomplete
              size="small"
              options={mockNavigationData.allProjects}
              getOptionLabel={(option) => option.name}
              renderInput={(params) => (
                <TextField {...params} placeholder="Switch to..." sx={{ minWidth: 200 }} />
              )}
              sx={{ flex: 1, maxWidth: 300 }}
            />

            <IconButton
              size="small"
              onClick={() => setMiniNavOpen(true)}
              title="Browse project items"
            >
              <MenuIcon />
            </IconButton>
          </Box>

          {/* Navigation arrows for quick switching */}
          <Box sx={{ display: 'flex', gap: 0.5 }}>
            <IconButton size="small" title="Previous item">
              <ArrowBackIcon fontSize="small" />
            </IconButton>
            <IconButton size="small" title="Next item">
              <ArrowForwardIcon fontSize="small" />
            </IconButton>
          </Box>
        </Box>
      </Paper>
    );
  }

  function FloatingMenuNavigation() {
    return (
      <>
        {/* Floating Speed Dial */}
        <SpeedDial
          ariaLabel="Navigation menu"
          sx={{ position: 'fixed', bottom: 24, left: 24, zIndex: 1000 }}
          icon={<SpeedDialIcon icon={<NavigationIcon />} openIcon={<CloseIcon />} />}
        >
          <SpeedDialAction
            icon={<HomeIcon />}
            tooltipTitle="Dashboard"
            onClick={() => console.log('Navigate to dashboard')}
          />
          <SpeedDialAction
            icon={<FolderIcon />}
            tooltipTitle="Project Overview"
            onClick={() => console.log('Navigate to project')}
          />
          <SpeedDialAction
            icon={<ViewListIcon />}
            tooltipTitle="Browse Items"
            onClick={() => setMiniNavOpen(true)}
          />
          <SpeedDialAction
            icon={<SearchIcon />}
            tooltipTitle="Search Projects"
            onClick={(e) => {
              setProjectSearchAnchor(e.currentTarget);
              setProjectSearchOpen(true);
            }}
          />
        </SpeedDial>

        {/* Floating breadcrumb */}
        <Paper sx={{
          position: 'fixed',
          top: 80,
          left: 24,
          p: 1.5,
          borderRadius: 3,
          zIndex: 999,
          maxWidth: 400
        }}>
          <Typography variant="body2" color="text.secondary" gutterBottom>
            Current Location:
          </Typography>
          <Breadcrumbs aria-label="breadcrumb" sx={{ fontSize: '0.875rem' }}>
            <Link underline="hover" color="inherit" href="#">
              {mockNavigationData.currentProject.name}
            </Link>
            <Typography color="text.primary" fontSize="0.875rem">
              {mockFeature.title}
            </Typography>
          </Breadcrumbs>
        </Paper>
      </>
    );
  }

  // Shared Navigation Components
  const MiniNavigationDrawer = () => (
    <Drawer
      anchor="left"
      open={miniNavOpen}
      onClose={() => setMiniNavOpen(false)}
      sx={{ zIndex: 1300 }}
    >
      <Box sx={{ width: 300, p: 2 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
          <Typography variant="h6">Project Navigation</Typography>
          <IconButton size="small" onClick={() => setMiniNavOpen(false)}>
            <CloseIcon />
          </IconButton>
        </Box>

        <Typography variant="subtitle2" color="primary" gutterBottom>
          Current Project: {mockNavigationData.currentProject.name}
        </Typography>

        <Typography variant="body2" fontWeight="medium" sx={{ mt: 2, mb: 1 }}>
          Features
        </Typography>
        <List dense>
          {mockNavigationData.currentProject.features.map((feature) => (
            <ListItem key={feature.id} disablePadding>
              <ListItemButton>
                <ListItemIcon>
                  <FeatureIcon color={feature.hasActiveTasks ? "primary" : "disabled"} />
                </ListItemIcon>
                <ListItemText
                  primary={feature.name}
                  secondary={feature.hasActiveTasks ? "Has active tasks" : "No active tasks"}
                />
                {feature.hasActiveTasks && (
                  <Chip label="Active" size="small" color="primary" />
                )}
              </ListItemButton>
            </ListItem>
          ))}
        </List>

        <Typography variant="body2" fontWeight="medium" sx={{ mt: 2, mb: 1 }}>
          Requirements
        </Typography>
        <List dense>
          {mockNavigationData.currentProject.requirements.map((requirement) => (
            <ListItem key={requirement.id} disablePadding>
              <ListItemButton>
                <ListItemIcon>
                  <AssignmentIcon color={requirement.hasActiveTasks ? "primary" : "disabled"} />
                </ListItemIcon>
                <ListItemText
                  primary={requirement.name}
                  secondary={requirement.hasActiveTasks ? "Has active tasks" : "No active tasks"}
                />
                {requirement.hasActiveTasks && (
                  <Chip label="Active" size="small" color="primary" />
                )}
              </ListItemButton>
            </ListItem>
          ))}
        </List>

        <Divider sx={{ my: 2 }} />

        <Typography variant="body2" fontWeight="medium" sx={{ mb: 1 }}>
          Recent Projects
        </Typography>
        <List dense>
          {mockNavigationData.recentProjects.map((project) => (
            <ListItem key={project.id} disablePadding>
              <ListItemButton>
                <ListItemIcon>
                  <FolderIcon />
                </ListItemIcon>
                <ListItemText primary={project.name} />
              </ListItemButton>
            </ListItem>
          ))}
        </List>
      </Box>
    </Drawer>
  );

  const ProjectSearchPopper = () => (
    <Popper
      open={projectSearchOpen}
      anchorEl={projectSearchAnchor}
      placement="bottom-start"
      transition
      sx={{ zIndex: 1300 }}
    >
      {({ TransitionProps }) => (
        <Grow {...TransitionProps}>
          <Paper sx={{ p: 2, minWidth: 300, maxWidth: 400 }}>
            <ClickAwayListener onClickAway={() => setProjectSearchOpen(false)}>
              <Box>
                <Typography variant="h6" gutterBottom>
                  Switch Project
                </Typography>

                <Autocomplete
                  options={mockNavigationData.allProjects}
                  getOptionLabel={(option) => option.name}
                  renderInput={(params) => (
                    <TextField
                      {...params}
                      placeholder="Search projects..."
                      autoFocus
                      size="small"
                    />
                  )}
                  renderOption={(props, option) => (
                    <Box component="li" {...props}>
                      <FolderIcon sx={{ mr: 1, color: 'text.secondary' }} />
                      {option.name}
                    </Box>
                  )}
                  onChange={(event, value) => {
                    if (value) {
                      console.log('Switch to project:', value.name);
                      setProjectSearchOpen(false);
                    }
                  }}
                />

                <Typography variant="body2" color="text.secondary" sx={{ mt: 2, mb: 1 }}>
                  Recent Projects
                </Typography>
                <Stack spacing={1}>
                  {mockNavigationData.recentProjects.slice(0, 3).map((project) => (
                    <Button
                      key={project.id}
                      variant="outlined"
                      size="small"
                      startIcon={<FolderIcon />}
                      onClick={() => {
                        console.log('Switch to recent project:', project.name);
                        setProjectSearchOpen(false);
                      }}
                      sx={{ justifyContent: 'flex-start' }}
                    >
                      {project.name}
                    </Button>
                  ))}
                </Stack>
              </Box>
            </ClickAwayListener>
          </Paper>
        </Grow>
      )}
    </Popper>
  );

};

export default CollaborativeLayoutTest3;
